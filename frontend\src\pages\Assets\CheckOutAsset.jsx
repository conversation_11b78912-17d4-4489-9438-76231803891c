import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import NavBar from "../../components/NavBar";
import "../../styles/Registration.css";
import TopSecFormPage from "../../components/TopSecFormPage";
import { useForm } from "react-hook-form";
import Alert from "../../components/Alert";
import assetsService from "../../services/assets-service";
import dtsService from "../../services/dts-integration-service";
import SystemLoading from "../../components/Loading/SystemLoading";

export default function CheckOutAsset() {
  const location = useLocation();
  const passedState = location.state;
  const currentDate = new Date().toISOString().split("T")[0];
  const conditionOptions = [
    { value: "1", label: "1 - Unserviceable" },
    { value: "2", label: "2 - Poor" },
    { value: "3", label: "3 - Needs Maintenance" },
    { value: "4", label: "4 - Functional" },
    { value: "5", label: "5 - Fair" },
    { value: "6", label: "6 - Good" },
    { value: "7", label: "7 - Very Good" },
    { value: "8", label: "8 - Excellent" },
    { value: "9", label: "9 - Like New" },
    { value: "10", label: "10 - Brand New" },
  ];

  const navigate = useNavigate();

  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (passedState) {
      console.log("Received state:", passedState);
    } else {
      console.warn("No state was passed!");
    }
  }, [passedState]);

  const {
    id,
    image,
    assetId,
    product,
    empId,
    employee,
    empLocation,
    checkoutDate,
    returnDate,
    ticketId,
    fromAsset,
  } = passedState || {};

  console.log({
    id,
    image,
    assetId,
    product,
    empId,
    employee,
    empLocation,
    checkoutDate,
    returnDate,
    ticketId,
    fromAsset,
  });

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isValid },
  } = useForm({
    mode: "all",
    defaultValues: {
      employee: employee || "",
      empLocation: empLocation || "",
      checkoutDate: checkoutDate || "",
      expectedReturnDate: returnDate || "",
      condition: "",
      notes: "",
      photos: [],
    },
  });

  useEffect(() => {
    const initialize = async () => {
      setIsLoading(true);
      try {
        setValue("employee", passedState.employee || "");
        setValue("empLocation", passedState.empLocation || "");
        setValue("checkoutDate", passedState.checkoutDate || "");
        setValue("expectedReturnDate", passedState.returnDate || "");
      } catch (error) {
        console.error("Error initializing:", error);
        setErrorMessage("Failed to initialize data");
      } finally {
        setIsLoading(false);
      }
    };

    initialize();
  }, [passedState, setValue]);

  const onSubmit = async (data) => {
    try {
      const formData = new FormData();

      formData.append("asset", id);
      formData.append("to_user_id", empId);
      formData.append("to_location", data.empLocation);
      formData.append("checkout_date", data.checkoutDate);
      formData.append("return_date", data.returnDate);

      const conditionValue = parseInt(data.condition, 10);
      if (!isNaN(conditionValue)) {
        formData.append("condition", conditionValue);
      }

      formData.append("notes", data.notes || "");
      formData.append("confirmation_notes", data.confirmationNotes || "");

      for (let pair of formData.entries()) {
        console.log(pair[0] + ": " + pair[1]);
      }

      await assetsService.createAssetCheckout(formData);
      await dtsService.resolveCheckoutTicket(ticketId);

      if (fromAsset) {
        console.log("Ticket Information:", { ticketId });

        navigate("/assets", {
          state: {
            successMessage: "Asset has been checked out successfully!",
          },
        });
      } else {
        navigate("/approved-tickets", {
          state: {
            successMessage: "Asset has been checked out successfully!",
          },
        });
      }
    } catch (error) {
      console.error("Error occured while checking out the asset:", error);
      setErrorMessage(
        error.message || "An error occurred while checking out the asset"
      );
    }
  };

  if (isLoading) {
    console.log("isLoading triggered — showing loading screen");
    return <SystemLoading />;
  }

  return (
    <>
      {errorMessage && <Alert message={errorMessage} type="danger" />}
      <nav>
        <NavBar />
      </nav>
      <main className="registration">
        <section className="top">
          <TopSecFormPage
            root={passedState?.fromAsset ? "Assets" : "Approved Tickets"}
            currentPage="Check-Out Asset"
            rootNavigatePage={
              passedState?.fromAsset ? "/assets" : "/approved-tickets"
            }
            title={assetId}
          />
        </section>
        <section className="registration-form">
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Employee */}
            <fieldset>
              <label htmlFor="employee">
                Employee <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="text"
                id="employee"
                readOnly
                {...register("employee")}
              />
            </fieldset>

            {/* Location */}
            <fieldset>
              <label htmlFor="empLocation">
                Location <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="text"
                id="empLocation"
                readOnly
                {...register("empLocation")}
              />
            </fieldset>

            {/* Check-Out Date */}
            <fieldset>
              <label htmlFor="checkoutDate">
                Check-Out Date <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="text"
                id="checkoutDate"
                readOnly
                {...register("checkoutDate")}
              />
            </fieldset>

            {/* Expected Return Date */}
            <fieldset>
              <label htmlFor="returnDate">
                Expected Return Date <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="date"
                id="returnDate"
                className={errors.returnDate ? "input-error" : ""}
                {...register("returnDate", {
                  required: "Expected return date is required",
                })}
                defaultValue={passedState?.returnDate || ""}
                min={currentDate}
              />
              {errors.returnDate && (
                <span className="error-message">
                  {errors.returnDate.message}
                </span>
              )}
            </fieldset>

            {/* Condition */}
            <fieldset>
              <label htmlFor="condition">
                Condition <span style={{ color: "red" }}>*</span>
              </label>
              <select
                id="condition"
                {...register("condition", {
                  required: "Condition is required",
                })}
                className={errors.condition ? "input-error" : ""}
              >
                <option value="">Select Condition</option>
                {conditionOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.condition && (
                <span className="error-message">
                  {errors.condition.message}
                </span>
              )}
            </fieldset>

            {/* Notes */}
            <fieldset>
              <label htmlFor="notes">Notes</label>
              <textarea
                id="notes"
                placeholder="Enter notes"
                {...register("notes")}
                rows="3"
                maxLength="500"
              ></textarea>
            </fieldset>

            {/* Submit */}
            <button
              type="submit"
              className="primary-button"
              disabled={!isValid}
            >
              Save
            </button>
          </form>
        </section>
      </main>
    </>
  );
}
