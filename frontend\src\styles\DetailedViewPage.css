.detailed-view-layout {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 100px 40px 40px 40px;
  background-color: #f5f6f8;
  min-height: calc(100vh - 100px);
}

.detailed-breadcrumb {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 12px;
}

.detailed-breadcrumb ul {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
}

.detailed-breadcrumb li {
  list-style: none;
}

.detailed-breadcrumb li+li::before {
  padding: 0px 6px 0px 8px;
  color: #666;
  content: "/\00a0";
}

.detailed-breadcrumb a {
  color: #666;
  text-decoration: none;
  cursor: pointer;
}

.detailed-breadcrumb a:hover {
  color: var(--primary-color);
}

.detailed-title-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #d3d3d3;
}

.detailed-title-section h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.detailed-subtitle {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

.detailed-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: white;
  border-radius: 8px 8px 0 0;
  padding: 0 20px;
}

.tab-button {
  padding: 15px 20px;
  background-color: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-button:hover {
  color: var(--primary-color);
}

.tab-actions {
  display: flex;
  gap: 10px;
}

.upload-btn {
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 0.875rem;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-btn:hover {
  background-color: #e9ecef;
}

.detailed-content-wrapper {
  display: flex;
  gap: 20px;
  flex: 1;
}

.detailed-main-content {
  flex: 1;
  background-color: white;
  border-radius: 0 0 8px 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  border-top: none;
}

.asset-details-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row label {
  width: 150px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
  flex-shrink: 0;
}

.detail-row span {
  font-size: 0.875rem;
  color: #666;
  flex: 1;
}

.status-with-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.manufacturer-links {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.manufacturer-links a {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.manufacturer-links a:hover {
  text-decoration: underline;
}

.detailed-sidebar {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-buttons-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.blue-btn {
  background-color: var(--primary-color);
  color: white;
}

.blue-btn:hover {
  background-color: var(--primary-color-hover);
}

.red-btn {
  background-color: #dc3545;
  color: white;
}

.red-btn:hover {
  background-color: #c82333;
}

.checked-out-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.checked-out-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.checked-out-info {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.profile-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.user-email {
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 4px;
}

.checkout-date {
  font-size: 0.75rem;
  color: #666;
}

.custom-action-buttons {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.custom-action-buttons .view-action-btn {
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.custom-action-buttons .view-action-btn.edit {
  background-color: var(--primary-color);
  color: white;
}

.custom-action-buttons .view-action-btn.edit:hover {
  background-color: var(--primary-color-hover);
}

.custom-action-buttons .view-action-btn.delete {
  background-color: #dc3545;
  color: white;
}

.custom-action-buttons .view-action-btn.delete:hover {
  background-color: #c82333;
}

@media (max-width: 1024px) {
  .detailed-content-wrapper {
    flex-direction: column;
  }

  .detailed-sidebar {
    width: 100%;
    flex-direction: row;
    gap: 20px;
  }

  .action-buttons-section,
  .checked-out-section,
  .custom-action-buttons {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .detailed-view-layout {
    padding: 100px 20px 20px 20px;
  }

  .detailed-sidebar {
    flex-direction: column;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-row label {
    width: auto;
  }

  .status-with-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .manufacturer-links {
    gap: 8px;
  }

  .detailed-tabs {
    flex-wrap: wrap;
    padding: 10px;
  }

  .tab-button {
    padding: 10px 15px;
  }

  .tab-actions {
    width: 100%;
    justify-content: center;
    margin-top: 10px;
  }
}